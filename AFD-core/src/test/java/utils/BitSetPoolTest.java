/**
 * BitSet对象池性能测试
 * 
 * <AUTHOR>  
 * @version 1.0
 * @since 2025/7/23
 */
package utils;

import java.util.ArrayList;
import java.util.BitSet;
import java.util.List;

public class BitSetPoolTest {

    public static void main(String[] args) {
        BitSetPoolTest test = new BitSetPoolTest();
        System.out.println("=== BitSet对象池性能测试 ===");
        
        test.testBasicPoolFunctionality();
        test.testPerformanceComparison();
        
        System.out.println("=== 测试完成 ===");
    }
    
    public void testBasicPoolFunctionality() {
        System.out.println("\n--- 测试1: 基本池功能 ---");
        
        BitSetPool pool = BitSetPool.getInstance();
        System.out.println("初始状态: " + pool.getPoolStats());
        
        // 获取几个BitSet对象
        List<BitSet> acquired = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            BitSet bitSet = pool.acquire();
            bitSet.set(i); // 设置一些位
            acquired.add(bitSet);
        }
        
        System.out.println("获取5个BitSet后: " + pool.getPoolStats());
        
        // 归还BitSet对象  
        for (BitSet bitSet : acquired) {
            pool.release(bitSet);
        }
        
        System.out.println("归还后: " + pool.getPoolStats());
        
        // 测试acquireCopy方法
        BitSet original = new BitSet();
        original.set(1);
        original.set(3);
        original.set(5);
        
        BitSet copy = pool.acquireCopy(original);
        System.out.println("原始BitSet: " + original);
        System.out.println("复制BitSet: " + copy);
        System.out.println("内容相同: " + original.equals(copy));
        
        pool.release(copy);
        System.out.println("基本功能测试完成");
    }
    
    public void testPerformanceComparison() {
        System.out.println("\n--- 测试2: 性能对比 ---");
        
        final int ITERATIONS = 100000;
        BitSetPool pool = BitSetPool.getInstance();
        
        // 测试传统clone方式
        long startTime = System.nanoTime();
        for (int i = 0; i < ITERATIONS; i++) {
            BitSet original = new BitSet(64);
            original.set(i % 64);
            
            // 传统方式：clone
            BitSet copy = (BitSet) original.clone();
            copy.set((i + 1) % 64);
            
            // 模拟使用后就丢弃（让GC处理）
        }
        long cloneTime = System.nanoTime() - startTime;
        
        // 测试对象池方式
        startTime = System.nanoTime();
        for (int i = 0; i < ITERATIONS; i++) {
            BitSet original = new BitSet(64);
            original.set(i % 64);
            
            // 对象池方式
            BitSet copy = pool.acquireCopy(original);
            copy.set((i + 1) % 64);
            
            // 归还到池中
            pool.release(copy);
        }
        long poolTime = System.nanoTime() - startTime;
        
        System.out.printf("传统clone方式: %.2f ms\n", cloneTime / 1_000_000.0);
        System.out.printf("对象池方式:     %.2f ms\n", poolTime / 1_000_000.0);
        System.out.printf("性能提升:       %.2f%%\n", 
                         ((double)(cloneTime - poolTime) / cloneTime) * 100);
        
        System.out.println("最终池状态: " + pool.getPoolStats());
        
        // 测试withBitSet便利方法
        startTime = System.nanoTime();
        BitSet testSource = new BitSet();
        testSource.set(10);
        testSource.set(20);
        
        for (int i = 0; i < 10000; i++) {
            int finalI = i;
            Integer result = pool.withBitSet(testSource, bitSet -> {
                bitSet.set(finalI % 64);
                return bitSet.cardinality();
            });
            // result会被自动处理，BitSet会被自动归还
        }
        long withBitSetTime = System.nanoTime() - startTime;
        
        System.out.printf("withBitSet便利方法: %.2f ms\n", withBitSetTime / 1_000_000.0);
        System.out.println("性能对比测试完成");
    }
}