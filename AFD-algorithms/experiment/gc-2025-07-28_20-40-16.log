[2025-07-28T20:40:16.349+0800][0.006s][info][gc] Using G1
[2025-07-28T20:40:16.358+0800][0.015s][info][gc,init] Version: 17.0.15+6-Ubuntu-0ubuntu120.04 (release)
[2025-07-28T20:40:16.358+0800][0.015s][info][gc,init] CPUs: 28 total, 28 available
[2025-07-28T20:40:16.359+0800][0.015s][info][gc,init] Memory: 15833M
[2025-07-28T20:40:16.359+0800][0.015s][info][gc,init] Large Page Support: Disabled
[2025-07-28T20:40:16.359+0800][0.016s][info][gc,init] NUMA Support: Disabled
[2025-07-28T20:40:16.359+0800][0.016s][info][gc,init] Compressed Oops: Enabled (Zero based)
[2025-07-28T20:40:16.360+0800][0.016s][info][gc,init] Heap Region Size: 16M
[2025-07-28T20:40:16.360+0800][0.017s][info][gc,init] Heap Min Capacity: 6G
[2025-07-28T20:40:16.360+0800][0.017s][info][gc,init] Heap Initial Capacity: 6G
[2025-07-28T20:40:16.360+0800][0.017s][info][gc,init] Heap Max Capacity: 6G
[2025-07-28T20:40:16.361+0800][0.017s][info][gc,init] Pre-touch: Disabled
[2025-07-28T20:40:16.361+0800][0.017s][info][gc,init] Parallel Workers: 20
[2025-07-28T20:40:16.361+0800][0.017s][info][gc,init] Concurrent Workers: 5
[2025-07-28T20:40:16.361+0800][0.017s][info][gc,init] Concurrent Refinement Workers: 20
[2025-07-28T20:40:16.361+0800][0.017s][info][gc,init] Periodic GC: Disabled
[2025-07-28T20:40:16.369+0800][0.025s][info][gc,metaspace] CDS archive(s) mapped at: [0x00007e9893000000-0x00007e9893bc7000-0x00007e9893bc7000), size 12349440, SharedBaseAddress: 0x00007e9893000000, ArchiveRelocationMode: 1.
[2025-07-28T20:40:16.369+0800][0.026s][info][gc,metaspace] Compressed class space mapped at: 0x00007e9894000000-0x00007e98d4000000, reserved size: 1073741824
[2025-07-28T20:40:16.369+0800][0.026s][info][gc,metaspace] Narrow klass base: 0x00007e9893000000, Narrow klass shift: 0, Narrow klass range: 0x100000000
[2025-07-28T20:40:16.416+0800][0.073s][info][gc,heap,exit] Heap
[2025-07-28T20:40:16.417+0800][0.073s][info][gc,heap,exit]  garbage-first heap   total 6291456K, used 32692K [0x0000000680000000, 0x0000000800000000)
[2025-07-28T20:40:16.417+0800][0.073s][info][gc,heap,exit]   region size 16384K, 1 young (16384K), 0 survivors (0K)
[2025-07-28T20:40:16.417+0800][0.074s][info][gc,heap,exit]  Metaspace       used 245K, committed 320K, reserved 1114112K
[2025-07-28T20:40:16.417+0800][0.074s][info][gc,heap,exit]   class space    used 18K, committed 64K, reserved 1048576K
