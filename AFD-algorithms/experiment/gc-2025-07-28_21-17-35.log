[2025-07-28T21:17:35.306+0800][0.006s][info][gc] Using G1
[2025-07-28T21:17:35.317+0800][0.017s][info][gc,init] Version: 17.0.15+6-Ubuntu-0ubuntu120.04 (release)
[2025-07-28T21:17:35.317+0800][0.017s][info][gc,init] CPUs: 28 total, 28 available
[2025-07-28T21:17:35.318+0800][0.017s][info][gc,init] Memory: 15833M
[2025-07-28T21:17:35.318+0800][0.017s][info][gc,init] Large Page Support: Disabled
[2025-07-28T21:17:35.318+0800][0.017s][info][gc,init] NUMA Support: Disabled
[2025-07-28T21:17:35.318+0800][0.017s][info][gc,init] Compressed Oops: Enabled (Zero based)
[2025-07-28T21:17:35.318+0800][0.018s][info][gc,init] Heap Region Size: 16M
[2025-07-28T21:17:35.318+0800][0.018s][info][gc,init] Heap Min Capacity: 6G
[2025-07-28T21:17:35.318+0800][0.018s][info][gc,init] Heap Initial Capacity: 6G
[2025-07-28T21:17:35.319+0800][0.018s][info][gc,init] Heap Max Capacity: 6G
[2025-07-28T21:17:35.319+0800][0.018s][info][gc,init] Pre-touch: Disabled
[2025-07-28T21:17:35.319+0800][0.018s][info][gc,init] Parallel Workers: 20
[2025-07-28T21:17:35.319+0800][0.018s][info][gc,init] Concurrent Workers: 5
[2025-07-28T21:17:35.319+0800][0.018s][info][gc,init] Concurrent Refinement Workers: 20
[2025-07-28T21:17:35.319+0800][0.019s][info][gc,init] Periodic GC: Disabled
[2025-07-28T21:17:35.328+0800][0.027s][info][gc,metaspace] CDS archive(s) mapped at: [0x00007cac03000000-0x00007cac03bc7000-0x00007cac03bc7000), size 12349440, SharedBaseAddress: 0x00007cac03000000, ArchiveRelocationMode: 1.
[2025-07-28T21:17:35.328+0800][0.027s][info][gc,metaspace] Compressed class space mapped at: 0x00007cac04000000-0x00007cac44000000, reserved size: 1073741824
[2025-07-28T21:17:35.328+0800][0.028s][info][gc,metaspace] Narrow klass base: 0x00007cac03000000, Narrow klass shift: 0, Narrow klass range: 0x100000000
[2025-07-28T21:17:35.735+0800][0.434s][info][gc,heap,exit] Heap
[2025-07-28T21:17:35.735+0800][0.434s][info][gc,heap,exit]  garbage-first heap   total 6291456K, used 65460K [0x0000000680000000, 0x0000000800000000)
[2025-07-28T21:17:35.735+0800][0.434s][info][gc,heap,exit]   region size 16384K, 3 young (49152K), 0 survivors (0K)
[2025-07-28T21:17:35.735+0800][0.434s][info][gc,heap,exit]  Metaspace       used 4694K, committed 4928K, reserved 1114112K
[2025-07-28T21:17:35.735+0800][0.435s][info][gc,heap,exit]   class space    used 635K, committed 768K, reserved 1048576K
