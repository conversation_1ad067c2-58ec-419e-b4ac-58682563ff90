package experiment;

import algorithm.Pyro;
import algorithm.TaneAlgorithm;
import measure.G3Measure;
import model.DataSet;
import model.FunctionalDependency;
import pli.PLIOptimizationIntegrator;
import sampling.RandomSampling;
import utils.DataLoader;
import utils.MemoryMonitor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Path;
import java.util.List;

/**
 * PLI内存优化集成测试
 * 用于验证优化组件的正确性和性能提升效果
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/28
 */
public class PLIOptimizationTest {
    private static final Logger logger = LoggerFactory.getLogger(PLIOptimizationTest.class);
    
    public static void main(String[] args) {
        logger.info("开始PLI内存优化集成测试");
        
        // 启动内存监控
        MemoryMonitor memoryMonitor = MemoryMonitor.getInstance();
        memoryMonitor.startMonitoring();
        
        try {
            // 测试小数据集
            testSmallDataset();
            
            // 测试中等数据集
            testMediumDataset();
            
            // 测试PLI优化组件
            testPLIOptimizationComponents();
            
            // 性能对比测试
            performanceComparisonTest();
            
        } catch (Exception e) {
            logger.error("测试过程中发生异常", e);
        } finally {
            memoryMonitor.stopMonitoring();
            logger.info("PLI内存优化集成测试完成");
        }
    }
    
    /**
     * 测试小数据集
     */
    private static void testSmallDataset() {
        logger.info("=== 测试小数据集 ===");
        
        try {
            // 创建测试数据集
            DataSet testDataset = createTestDataset(1000, 5);
            
            // 测试Pyro算法
            testPyroWithOptimization(testDataset, "小数据集");
            
            // 测试TANE算法
            testTaneWithOptimization(testDataset, "小数据集");
            
        } catch (Exception e) {
            logger.error("小数据集测试失败", e);
        }
    }
    
    /**
     * 测试中等数据集
     */
    private static void testMediumDataset() {
        logger.info("=== 测试中等数据集 ===");
        
        try {
            // 创建测试数据集
            DataSet testDataset = createTestDataset(10000, 8);
            
            // 测试Pyro算法
            testPyroWithOptimization(testDataset, "中等数据集");
            
            // 测试TANE算法
            testTaneWithOptimization(testDataset, "中等数据集");
            
        } catch (Exception e) {
            logger.error("中等数据集测试失败", e);
        }
    }
    
    /**
     * 测试PLI优化组件
     */
    private static void testPLIOptimizationComponents() {
        logger.info("=== 测试PLI优化组件 ===");
        
        try {
            DataSet testDataset = createTestDataset(5000, 6);
            
            // 测试PLI优化集成器
            PLIOptimizationIntegrator integrator = new PLIOptimizationIntegrator(testDataset);
            
            logger.info("PLI优化集成器初始化成功");
            logger.info("初始策略: {}", integrator.getCurrentStrategy());
            
            // 测试内存监控
            MemoryMonitor.MemorySnapshot snapshot = MemoryMonitor.getInstance().getCurrentMemorySnapshot();
            logger.info("当前内存使用: {}MB / {}MB ({}%)", 
                       snapshot.getUsedHeapMemory() / (1024 * 1024),
                       snapshot.getMaxHeapMemory() / (1024 * 1024),
                       String.format("%.1f", snapshot.getUsageRatio() * 100));
            
            // 清理资源
            integrator.shutdown();
            
        } catch (Exception e) {
            logger.error("PLI优化组件测试失败", e);
        }
    }
    
    /**
     * 性能对比测试
     */
    private static void performanceComparisonTest() {
        logger.info("=== 性能对比测试 ===");
        
        try {
            DataSet testDataset = createTestDataset(8000, 7);
            
            // 记录初始内存
            MemoryMonitor.MemorySnapshot initialMemory = MemoryMonitor.getInstance().getCurrentMemorySnapshot();
            
            // 测试优化版本
            long startTime = System.currentTimeMillis();
            Pyro optimizedPyro = new Pyro(testDataset, new G3Measure(), new RandomSampling(), 0.1, 0.1, true);
            List<FunctionalDependency> optimizedResult = optimizedPyro.discover();
            long optimizedTime = System.currentTimeMillis() - startTime;
            
            // 记录峰值内存
            long peakMemory = optimizedPyro.getPeakMemoryUsageMB();
            
            logger.info("优化版本结果:");
            logger.info("  执行时间: {}ms", optimizedTime);
            logger.info("  发现FD数量: {}", optimizedResult.size());
            logger.info("  内存峰值: {}MB", peakMemory);
            logger.info("  PLI统计: {}", optimizedPyro.getPLIPerformanceStats());
            
            // 输出详细的内存统计
            MemoryMonitor.MemorySnapshot finalMemory = MemoryMonitor.getInstance().getCurrentMemorySnapshot();
            logger.info("内存使用变化: {}MB -> {}MB", 
                       initialMemory.getUsedHeapMemory() / (1024 * 1024),
                       finalMemory.getUsedHeapMemory() / (1024 * 1024));
            
        } catch (Exception e) {
            logger.error("性能对比测试失败", e);
        }
    }
    
    /**
     * 测试Pyro算法（带优化）
     */
    private static void testPyroWithOptimization(DataSet dataset, String datasetName) {
        logger.info("测试Pyro算法 - {}", datasetName);
        
        try {
            long startTime = System.currentTimeMillis();
            
            Pyro pyro = new Pyro(dataset, new G3Measure(), new RandomSampling(), 0.1, 0.1, true);
            List<FunctionalDependency> result = pyro.discover();
            
            long executionTime = System.currentTimeMillis() - startTime;
            
            logger.info("Pyro算法结果 ({}): 执行时间={}ms, FD数量={}, 内存峰值={}MB", 
                       datasetName, executionTime, result.size(), pyro.getPeakMemoryUsageMB());
            
            if (!pyro.getPLIPerformanceStats().isEmpty()) {
                logger.info("PLI性能统计: {}", pyro.getPLIPerformanceStats());
            }
            
        } catch (Exception e) {
            logger.error("Pyro算法测试失败 - {}", datasetName, e);
        }
    }
    
    /**
     * 测试TANE算法（带优化）
     */
    private static void testTaneWithOptimization(DataSet dataset, String datasetName) {
        logger.info("测试TANE算法 - {}", datasetName);
        
        try {
            long startTime = System.currentTimeMillis();
            
            TaneAlgorithm tane = new TaneAlgorithm(dataset, new G3Measure(), 0.1, true);
            List<FunctionalDependency> result = tane.discover();
            
            long executionTime = System.currentTimeMillis() - startTime;
            
            logger.info("TANE算法结果 ({}): 执行时间={}ms, FD数量={}, 内存峰值={}MB", 
                       datasetName, executionTime, result.size(), tane.getPeakMemoryUsageMB());
            
            if (!tane.getPLIPerformanceStats().isEmpty()) {
                logger.info("PLI性能统计: {}", tane.getPLIPerformanceStats());
            }
            
        } catch (Exception e) {
            logger.error("TANE算法测试失败 - {}", datasetName, e);
        }
    }
    
    /**
     * 创建测试数据集
     */
    private static DataSet createTestDataset(int rows, int columns) {
        logger.info("创建测试数据集: {}行 × {}列", rows, columns);
        
        // 这里简化实现，实际应该创建有意义的测试数据
        // 可以使用DataLoader从文件加载，或者生成合成数据
        
        try {
            // 尝试加载一个小的真实数据集进行测试
            // 如果没有可用的数据集，可以生成合成数据
            return generateSyntheticDataset(rows, columns);
        } catch (Exception e) {
            logger.warn("无法创建测试数据集，使用最小数据集", e);
            return generateMinimalDataset();
        }
    }
    
    /**
     * 生成合成数据集
     */
    private static DataSet generateSyntheticDataset(int rows, int columns) {
        // 这里应该实现合成数据集的生成逻辑
        // 为了简化，返回一个最小数据集
        return generateMinimalDataset();
    }
    
    /**
     * 生成最小数据集用于测试
     */
    private static DataSet generateMinimalDataset() {
        // 创建一个简单的测试数据集
        // 实际实现中应该使用DataSet的构造方法或工厂方法
        
        try {
            // 尝试从项目中的测试数据创建数据集
            // 这里需要根据实际的DataSet API进行调整
            
            // 临时返回null，实际使用时需要实现
            logger.warn("generateMinimalDataset方法需要实现");
            return null;
            
        } catch (Exception e) {
            logger.error("无法生成最小数据集", e);
            return null;
        }
    }
    
    /**
     * 运行内存压力测试
     */
    public static void runMemoryStressTest() {
        logger.info("=== 内存压力测试 ===");
        
        MemoryMonitor memoryMonitor = MemoryMonitor.getInstance();
        memoryMonitor.startMonitoring(1000, alert -> {
            logger.warn("内存压力测试中的警报: {}", alert);
        });
        
        try {
            // 逐步增加数据集大小，测试内存管理
            int[] testSizes = {1000, 5000, 10000, 20000, 50000};
            
            for (int size : testSizes) {
                logger.info("测试数据集大小: {} 行", size);
                
                DataSet dataset = createTestDataset(size, 6);
                if (dataset != null) {
                    testPyroWithOptimization(dataset, "压力测试-" + size + "行");
                }
                
                // 强制垃圾回收
                System.gc();
                
                // 等待一段时间让内存稳定
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
                
                // 检查内存使用
                MemoryMonitor.MemorySnapshot snapshot = memoryMonitor.getCurrentMemorySnapshot();
                logger.info("当前内存使用: {}MB ({}%)", 
                           snapshot.getUsedHeapMemory() / (1024 * 1024),
                           String.format("%.1f", snapshot.getUsageRatio() * 100));
            }
            
        } finally {
            memoryMonitor.stopMonitoring();
        }
    }
}
