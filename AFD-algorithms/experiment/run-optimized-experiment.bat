@echo off
setlocal enabledelayedexpansion

REM AFD-measures PLI优化实验启动脚本 (Windows版本)
REM 此脚本自动检测系统配置并设置最优的JVM参数

echo ============================================================
echo                   PLI优化实验启动器
echo ============================================================

REM 脚本配置
set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%..
set EXPERIMENT_JAR=%SCRIPT_DIR%target\experiment-1.0-SNAPSHOT.jar
set CONFIG_FILE=%SCRIPT_DIR%pli-optimization.properties

REM 检测系统内存 (Windows)
echo [INFO] 检测系统配置...

REM 使用wmic获取系统内存 (以字节为单位)
for /f "skip=1" %%i in ('wmic computersystem get TotalPhysicalMemory') do (
    if not "%%i"=="" (
        set /a TOTAL_MEMORY_MB=%%i/1024/1024
        goto :memory_detected
    )
)

REM 如果wmic失败，使用默认值
set TOTAL_MEMORY_MB=8192
echo [WARN] 无法检测系统内存，使用默认值: 8192MB

:memory_detected
echo [INFO] 检测到系统内存: %TOTAL_MEMORY_MB%MB

REM 计算可用内存 (为系统保留25%)
set /a AVAILABLE_MEMORY_MB=%TOTAL_MEMORY_MB%*3/4

REM 根据内存大小配置JVM参数
if %AVAILABLE_MEMORY_MB% geq 16384 (
    REM 16GB+ 系统
    set HEAP_SIZE=12g
    set NEW_SIZE=3g
    set REGION_SIZE=32m
    set PAUSE_TARGET=200
    echo [INFO] 配置为超大内存模式 ^(16GB+^)
) else if %AVAILABLE_MEMORY_MB% geq 8192 (
    REM 8GB+ 系统
    set HEAP_SIZE=6g
    set NEW_SIZE=1536m
    set REGION_SIZE=16m
    set PAUSE_TARGET=200
    echo [INFO] 配置为大内存模式 ^(8GB+^)
) else if %AVAILABLE_MEMORY_MB% geq 4096 (
    REM 4GB+ 系统
    set HEAP_SIZE=3g
    set NEW_SIZE=768m
    set REGION_SIZE=8m
    set PAUSE_TARGET=300
    echo [INFO] 配置为中等内存模式 ^(4GB+^)
) else if %AVAILABLE_MEMORY_MB% geq 2048 (
    REM 2GB+ 系统
    set HEAP_SIZE=1536m
    set NEW_SIZE=384m
    set REGION_SIZE=4m
    set PAUSE_TARGET=500
    echo [INFO] 配置为小内存模式 ^(2GB+^)
) else (
    REM <2GB 系统
    set HEAP_SIZE=1g
    set NEW_SIZE=256m
    set REGION_SIZE=2m
    set PAUSE_TARGET=1000
    echo [WARN] 系统内存较小，可能影响大数据集处理性能
)

REM 计算PLI缓存限制
set /a PLI_CACHE_MB=%AVAILABLE_MEMORY_MB%/4
set /a PLI_CLEANUP_MB=%AVAILABLE_MEMORY_MB%/5

REM 检查Java
echo [INFO] 检查Java环境...
java -version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] 未找到Java，请安装Java 8或更高版本
    pause
    exit /b 1
)

REM 获取Java版本
for /f "tokens=3" %%i in ('java -version 2^>^&1 ^| findstr /i version') do (
    set JAVA_VERSION=%%i
    goto :java_found
)
:java_found
echo [INFO] Java版本: %JAVA_VERSION%

REM 检查实验JAR文件
if not exist "%EXPERIMENT_JAR%" (
    echo [WARN] 未找到实验JAR文件: %EXPERIMENT_JAR%
    echo [INFO] 请先构建项目: mvn clean package -DskipTests
    pause
    exit /b 1
)

REM 检查配置文件
if exist "%CONFIG_FILE%" (
    echo [INFO] 使用配置文件: %CONFIG_FILE%
) else (
    echo [WARN] 未找到配置文件: %CONFIG_FILE%
    echo [INFO] 使用默认配置
)

REM 生成时间戳用于GC日志
for /f "tokens=1-4 delims=/ " %%i in ('date /t') do set DATE_STAMP=%%l%%j%%k
for /f "tokens=1-2 delims=: " %%i in ('time /t') do set TIME_STAMP=%%i%%j
set TIMESTAMP=%DATE_STAMP%-%TIME_STAMP%

REM 显示配置信息
echo.
echo ============================================================
echo                   PLI优化实验配置
echo ============================================================
echo 系统内存:     %TOTAL_MEMORY_MB%MB
echo 可用内存:     %AVAILABLE_MEMORY_MB%MB
echo 堆内存大小:   %HEAP_SIZE%
echo 新生代大小:   %NEW_SIZE%
echo GC暂停目标:   %PAUSE_TARGET%ms
echo PLI缓存限制:  %PLI_CACHE_MB%MB
echo ============================================================
echo.

REM 构建JVM参数
set JVM_ARGS=-Xms%HEAP_SIZE% -Xmx%HEAP_SIZE%
set JVM_ARGS=%JVM_ARGS% -XX:NewSize=%NEW_SIZE% -XX:MaxNewSize=%NEW_SIZE%
set JVM_ARGS=%JVM_ARGS% -XX:+UseG1GC
set JVM_ARGS=%JVM_ARGS% -XX:MaxGCPauseMillis=%PAUSE_TARGET%
set JVM_ARGS=%JVM_ARGS% -XX:G1HeapRegionSize=%REGION_SIZE%
set JVM_ARGS=%JVM_ARGS% -XX:G1NewSizePercent=20
set JVM_ARGS=%JVM_ARGS% -XX:G1MaxNewSizePercent=30
set JVM_ARGS=%JVM_ARGS% -XX:G1MixedGCCountTarget=8
set JVM_ARGS=%JVM_ARGS% -XX:G1MixedGCLiveThresholdPercent=85
set JVM_ARGS=%JVM_ARGS% -XX:+UnlockExperimentalVMOptions
set JVM_ARGS=%JVM_ARGS% -XX:+UseStringDeduplication
set JVM_ARGS=%JVM_ARGS% -XX:+OptimizeStringConcat
set JVM_ARGS=%JVM_ARGS% -XX:+PrintGC
set JVM_ARGS=%JVM_ARGS% -XX:+PrintGCDetails
set JVM_ARGS=%JVM_ARGS% -XX:+PrintGCTimeStamps
set JVM_ARGS=%JVM_ARGS% -XX:+PrintGCApplicationStoppedTime
set JVM_ARGS=%JVM_ARGS% -Xloggc:gc-%TIMESTAMP%.log

REM PLI优化配置
set JVM_ARGS=%JVM_ARGS% -Dpli.cache.max.memory.mb=%PLI_CACHE_MB%
set JVM_ARGS=%JVM_ARGS% -Dpli.cache.cleanup.threshold.mb=%PLI_CLEANUP_MB%
set JVM_ARGS=%JVM_ARGS% -Dmemory.monitor.enabled=true
set JVM_ARGS=%JVM_ARGS% -Dmemory.monitor.warning.threshold=0.7
set JVM_ARGS=%JVM_ARGS% -Dmemory.monitor.critical.threshold=0.85

REM 如果内存较小，启用强制流式处理
if %AVAILABLE_MEMORY_MB% lss 2048 (
    set JVM_ARGS=%JVM_ARGS% -Dpli.force.streaming=true
    echo [INFO] 启用强制流式处理模式
)

REM 显示启动信息
echo [INFO] 启动PLI优化实验...
echo [INFO] 使用JVM参数: %JVM_ARGS%
echo.

REM 运行实验
java %JVM_ARGS% -jar "%EXPERIMENT_JAR%" %*

REM 检查执行结果
if errorlevel 1 (
    echo.
    echo [ERROR] 实验执行失败，错误代码: %errorlevel%
    echo [INFO] 请检查日志文件和错误信息
) else (
    echo.
    echo [SUCCESS] 实验执行完成
)

echo.
echo 按任意键退出...
pause >nul
