[2025-07-28T21:15:46.351+0800][0.012s][info][gc] Using G1
[2025-07-28T21:15:46.361+0800][0.022s][info][gc,init] Version: 17.0.15+6-Ubuntu-0ubuntu120.04 (release)
[2025-07-28T21:15:46.361+0800][0.022s][info][gc,init] CPUs: 28 total, 28 available
[2025-07-28T21:15:46.362+0800][0.023s][info][gc,init] Memory: 15833M
[2025-07-28T21:15:46.362+0800][0.023s][info][gc,init] Large Page Support: Disabled
[2025-07-28T21:15:46.362+0800][0.023s][info][gc,init] NUMA Support: Disabled
[2025-07-28T21:15:46.362+0800][0.023s][info][gc,init] Compressed Oops: Enabled (Zero based)
[2025-07-28T21:15:46.362+0800][0.023s][info][gc,init] Heap Region Size: 16M
[2025-07-28T21:15:46.363+0800][0.024s][info][gc,init] Heap Min Capacity: 6G
[2025-07-28T21:15:46.363+0800][0.024s][info][gc,init] Heap Initial Capacity: 6G
[2025-07-28T21:15:46.363+0800][0.024s][info][gc,init] Heap Max Capacity: 6G
[2025-07-28T21:15:46.363+0800][0.024s][info][gc,init] Pre-touch: Disabled
[2025-07-28T21:15:46.363+0800][0.024s][info][gc,init] Parallel Workers: 20
[2025-07-28T21:15:46.363+0800][0.024s][info][gc,init] Concurrent Workers: 5
[2025-07-28T21:15:46.364+0800][0.024s][info][gc,init] Concurrent Refinement Workers: 20
[2025-07-28T21:15:46.364+0800][0.024s][info][gc,init] Periodic GC: Disabled
[2025-07-28T21:15:46.369+0800][0.029s][info][gc,metaspace] CDS archive(s) mapped at: [0x00007db87f000000-0x00007db87fbc7000-0x00007db87fbc7000), size 12349440, SharedBaseAddress: 0x00007db87f000000, ArchiveRelocationMode: 1.
[2025-07-28T21:15:46.369+0800][0.030s][info][gc,metaspace] Compressed class space mapped at: 0x00007db880000000-0x00007db8c0000000, reserved size: 1073741824
[2025-07-28T21:15:46.369+0800][0.030s][info][gc,metaspace] Narrow klass base: 0x00007db87f000000, Narrow klass shift: 0, Narrow klass range: 0x100000000
[2025-07-28T21:15:46.405+0800][0.066s][info][gc,heap,exit] Heap
[2025-07-28T21:15:46.405+0800][0.066s][info][gc,heap,exit]  garbage-first heap   total 6291456K, used 32692K [0x0000000680000000, 0x0000000800000000)
[2025-07-28T21:15:46.406+0800][0.066s][info][gc,heap,exit]   region size 16384K, 1 young (16384K), 0 survivors (0K)
[2025-07-28T21:15:46.406+0800][0.066s][info][gc,heap,exit]  Metaspace       used 245K, committed 320K, reserved 1114112K
[2025-07-28T21:15:46.406+0800][0.067s][info][gc,heap,exit]   class space    used 18K, committed 64K, reserved 1048576K
